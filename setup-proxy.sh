#!/bin/bash

# VoxForge TTS Docker 代理设置脚本
# 使用方法: source setup-proxy.sh

# 颜色定义
GREEN="\033[0;32m"
YELLOW="\033[1;33m"
BLUE="\033[0;34m"
RED="\033[0;31m"
NC="\033[0m" # No Color

echo -e "${BLUE}=== VoxForge TTS Docker 代理设置 ===${NC}"

# 检测操作系统并设置正确的宿主机地址
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS - Docker Desktop 使用 host.docker.internal
    HOST_IP="host.docker.internal"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux - 使用宿主机网络接口地址
    HOST_IP=$(ip route show default | awk '/default/ {print $3}' | head -n1)
    if [ -z "$HOST_IP" ]; then
        HOST_IP="**********"  # Docker 默认网关
    fi
else
    # Windows 或其他系统
    HOST_IP="host.docker.internal"
fi

echo -e "${BLUE}检测到宿主机地址: ${YELLOW}$HOST_IP${NC}"

# 设置代理环境变量
export https_proxy=http://$HOST_IP:7890
export http_proxy=http://$HOST_IP:7890
export all_proxy=socks5://$HOST_IP:7890

# 设置大写版本（某些工具需要）
export HTTPS_PROXY=$https_proxy
export HTTP_PROXY=$http_proxy
export ALL_PROXY=$all_proxy

echo -e "${GREEN}代理环境变量已设置:${NC}"
echo -e "  HTTP_PROXY: ${YELLOW}$HTTP_PROXY${NC}"
echo -e "  HTTPS_PROXY: ${YELLOW}$HTTPS_PROXY${NC}"
echo -e "  ALL_PROXY: ${YELLOW}$ALL_PROXY${NC}"
echo ""
echo -e "${BLUE}现在可以运行 Docker 构建命令:${NC}"
echo -e "  ${YELLOW}./docker-build.sh${NC}"
echo ""
echo -e "${YELLOW}注意: 请确保代理服务器 127.0.0.1:7890 正在运行${NC}"
