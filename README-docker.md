# VoxForge TTS Docker 部署指南

## 项目简介

VoxForge TTS 是一个基于 Kokoro 模型的中文语音合成 API 服务。本文档提供了使用 Docker 和 docker-compose 部署该服务的详细说明。

## 功能特性

- 基于 Kokoro 模型的高质量中文语音合成
- RESTful API 接口，易于集成到其他应用
- 支持多种中文音色（男声/女声）
- 支持语速调整
- Docker 容器化部署，简化运维

## 快速开始

### 使用 docker-compose 部署

1. 确保已安装 Docker 和 docker-compose

2. 克隆项目仓库（如果尚未克隆）

3. 在项目根目录下运行：

```bash
docker-compose up -d
```

这将构建 Docker 镜像并启动服务。首次构建可能需要几分钟时间。

4. 服务启动后，可以通过 `http://localhost:8000` 访问 API

### 手动构建 Docker 镜像

如果需要手动构建 Docker 镜像，可以使用以下命令：

```bash
docker build -t voxforge-tts:1.0 .
```

## API 接口说明

### 1. 文本转语音

- **URL**: `/tts`
- **方法**: POST
- **请求体**:

```json
{
  "text": "要转换的中文文本",
  "voice": "zf_002",  // 可选，默认为 zf_002
  "speed": 1.0,       // 可选，默认为 1.0
  "output_path": ""  // 可选，完整输出路径，默认自动生成
}
```

- **参数说明**:
  - `text`: 必填，要转换为语音的中文文本
  - `voice`: 可选，指定使用的音色ID，默认为 `zf_002`
  - `speed`: 可选，语速调整因子，默认为 1.0，值越大语速越快
  - `output_path`: 可选，指定生成音频文件的完整路径
    - 如果不提供，将自动生成一个唯一文件名并保存到 `./output/` 目录
    - 如果提供，将使用指定的完整路径，系统会自动创建必要的目录

- **响应**:

```json
{
  "success": true,
  "message": "语音生成成功",
  "output_path": "指定或自动生成的输出路径",
  "file_exists": true,
  "file_size_bytes": 12345,
  "generation_time_seconds": 1.23,
  "chunk_count": 2,
  "voice_used": "zf_002",
  "voice_name": "🚺 默认女声",
  "speed": 1.0
}
```

- **响应说明**:
  - `success`: 是否成功生成语音
  - `message`: 操作结果描述
  - `output_path`: 生成的音频文件路径
  - `file_exists`: 文件是否成功创建
  - `file_size_bytes`: 文件大小（字节）
  - `generation_time_seconds`: 生成耗时（秒）
  - `chunk_count`: 处理的音频片段数量
  - `voice_used`: 使用的音色ID
  - `voice_name`: 音色的显示名称
  - `speed`: 使用的语速值

### 2. 获取可用音色列表

- **URL**: `/voices`
- **方法**: GET
- **响应**:

```json
{
  "available_voices": {
    "zf_xiaobei": "🚺 小贝",
    "zf_xiaoni": "🚺 小妮",
    "zf_xiaoxiao": "🚺 小小",
    "zf_xiaoyi": "🚺 小艺",
    "zm_yunjian": "🚹 云健",
    "zm_yunxi": "🚹 云希",
    "zm_yunxia": "🚹 云霞",
    "zm_yunyang": "🚹 云阳",
    "zf_002": "🚺 默认女声"
  }
}
```

### 3. 健康检查

- **URL**: `/health`
- **方法**: GET
- **响应**:

```json
{
  "status": "healthy",
  "service": "VoxForge TTS API",
  "model_ready": true
}
```

## Docker 配置说明

### 环境变量

在 docker-compose.yml 中可以配置以下环境变量：

- `TZ`: 时区设置，默认为 Asia/Shanghai
- `PYTHONUNBUFFERED`: 设置为 1 确保 Python 输出不被缓冲，实时显示日志
- `LOG_LEVEL`: 日志级别设置，可选值为 DEBUG、INFO、WARNING、ERROR、CRITICAL，默认为 INFO

### 端口映射

默认将容器内的 8000 端口映射到主机的 8000 端口。如需修改，请编辑 docker-compose.yml 文件中的 ports 部分：

```yaml
ports:
  - "<主机端口>:8000"
```

### 数据卷

服务会将生成的音频文件保存在容器内的 `/app/output` 目录。默认配置将此目录映射到主机的 `./output` 目录：

```yaml
volumes:
  - ./output:/app/output
```

## 维护与故障排除

### 日志管理

#### 查看实时日志

```bash
docker-compose logs -f
```

#### 查看特定数量的日志行

```bash
docker-compose logs --tail=100
```

#### 调整日志级别

在 docker-compose.yml 文件中修改 LOG_LEVEL 环境变量：

```yaml
environment:
  - LOG_LEVEL=DEBUG  # 可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL
```

修改后重启服务以应用更改：

```bash
docker-compose down
docker-compose up -d
```

#### 日志说明

- **DEBUG**: 详细的调试信息，包括请求头、请求体和音频处理细节
- **INFO**: 标准操作信息，包括请求处理、音频生成状态等
- **WARNING**: 警告信息，如参数验证失败等
- **ERROR**: 错误信息，如音频生成失败等
- **CRITICAL**: 严重错误，影响系统运行的问题

### 重启服务

```bash
docker-compose restart
```

### 停止服务

```bash
docker-compose down
```

### 常见问题

1. **服务无法启动**
   - 检查端口 8000 是否被占用
   - 检查 Docker 和 docker-compose 是否正确安装
   - 查看启动日志：`docker-compose logs`

2. **API 请求失败**
   - 检查服务是否正常运行
   - 检查请求格式是否正确
   - 查看容器日志获取详细错误信息

3. **日志不显示或延迟显示**
   - 确认 `PYTHONUNBUFFERED=1` 环境变量已设置
   - 尝试重启容器：`docker-compose restart`
   - 检查 Docker 日志驱动配置

4. **递归深度超限错误**
   - 尝试分段处理较长的文本
   - 检查文本中是否包含特殊字符或格式
   - 增加日志级别到 DEBUG 以获取更多信息：`LOG_LEVEL=DEBUG`

5. **日志过多导致磁盘空间问题**
   - 定期清理日志：`docker-compose logs --no-color | grep -v "DEBUG" > filtered_logs.txt`
   - 配置 Docker 日志轮转：编辑 Docker 守护进程配置文件，添加日志轮转策略

## 性能优化

- 首次请求可能较慢，因为需要加载模型
- 后续请求会更快，因为模型已加载到内存中
- 对于高负载场景，可以考虑增加容器资源限制

## 安全注意事项

- 默认配置将 API 暴露在 0.0.0.0 上，可以从任何 IP 访问
- 在生产环境中，建议添加适当的身份验证和授权机制
- 考虑使用反向代理（如 Nginx）提供额外的安全层