# 使用官方 Python 镜像作为基础镜像
FROM python:3.12-slim AS voxforge-tts

# 定义代理参数
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG ALL_PROXY

# 设置工作目录
WORKDIR /app

# 安装系统依赖，soundfile 需要 libsndfile1
# 先尝试不使用代理安装，如果失败再使用代理
RUN apt-get update && apt-get install -y --no-install-recommends \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/* || \
    (if [ -n "$HTTP_PROXY" ]; then \
        echo "Acquire::http::Proxy \"$HTTP_PROXY\";" > /etc/apt/apt.conf.d/proxy.conf; \
    fi && \
    if [ -n "$HTTPS_PROXY" ]; then \
        echo "Acquire::https::Proxy \"$HTTPS_PROXY\";" >> /etc/apt/apt.conf.d/proxy.conf; \
    fi && \
    apt-get update && apt-get install -y --no-install-recommends \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/* \
    && rm -f /etc/apt/apt.conf.d/proxy.conf)

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
# 使用代理设置进行 pip 安装
# 先安装CPU版本的PyTorch，然后安装其他依赖（排除torch）
RUN if [ -n "$HTTP_PROXY" ]; then \
        pip config set global.proxy "$HTTP_PROXY"; \
    fi && \
    pip install --no-cache-dir torch==2.7.1+cpu --index-url https://download.pytorch.org/whl/cpu && \
    grep -v "^torch==" requirements.txt > requirements_no_torch.txt && \
    pip install --no-cache-dir --no-deps -r requirements_no_torch.txt

# 下载 spaCy 模型，使用代理设置
RUN if [ -n "$HTTP_PROXY" ]; then \
        export http_proxy="$HTTP_PROXY"; \
    fi && \
    if [ -n "$HTTPS_PROXY" ]; then \
        export https_proxy="$HTTPS_PROXY"; \
    fi && \
    if [ -n "$ALL_PROXY" ]; then \
        export all_proxy="$ALL_PROXY"; \
    fi && \
    python3 -m spacy download en_core_web_sm

# 复制模型准备脚本
COPY *.py .

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV KOKORO_MODEL_PATH="/app/3rd/Kokoro/"
# 强制使用CPU模式，避免CUDA库依赖问题
ENV CUDA_VISIBLE_DEVICES=""
ENV TORCH_CUDA_ARCH_LIST=""
ENV FORCE_CUDA="0"

# 复制3rd目录（预下载的模型）
COPY 3rd/Kokoro 3rd/Kokoro

# 在构建时执行模型文件修补（不下载模型）
# RUN python3 -c "import prepare; prepare.patch_kokoro_files()"

# 设置容器元数据
LABEL maintainer="VoxForge Team" \
      description="VoxForge TTS API服务" \
      version="1.0"

# 暴露服务端口
EXPOSE 8000

# 设置默认执行命令
CMD ["python3", "main.py"]
