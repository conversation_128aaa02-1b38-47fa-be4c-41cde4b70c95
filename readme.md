# VoxForge

**VoxForge** = Vox（拉丁语"声音"）+ Forge（锻造）→ "声音锻造厂"

## 项目简介

一个命令行工具，用于处理从 Hugging Face 下载的语音合成模型。

## 项目结构

- `src/` - 代码路径
- `main.c` - 主程序入口
- `CMakeLists.txt` - 构建配置

## 功能特性



先登录私服 如果登录过了就不用登录了
harbor登录账号密码：用户名 saida 密码 NXun9^#@
登录harbor：
docker login sdharbor.sdvideo.cn
docker login ********

mvn clean package -Dmaven.test.skip=true

把打包后的target/analysis-system.jar 拷贝到Dockerfile所在目录

[0.2] 代表的是你要打包的版本号

doc/Dockerfile 
来这个地方打包
命令打包：
docker build -t voxforge:v0.2 .
如果你是mac 还是打x64 镜像
docker buildx build --platform linux/amd64 --tag sdharbor.sdvideo.cn/av-group/voxforge:v0.2 --push  .
docker build --tag sdharbor.sdvideo.cn/av-group/analysis-system-arm-jre8:v0.2 --push  .

没网络的情况下这样来
docker save -o voxforge-v0.2.tar sdharbor.sdvideo.cn/av-group/voxforge:v0.2
docker load -i voxforge-v0.2.tar


如果是idea 的话直接把voxforge:v0.2 放在 配置的image Tag里面

然后打tag：  这个地方要打到对应域名上
docker tag voxforge:v0.2 sdharbor.sdvideo.cn/av-group/voxforge:v0.2

然后就能推送了

docker push sdharbor.sdvideo.cn/av-group/voxforge:v0.2


Docker 推送命令：
在项目中标记镜像：
docker tag voxforge[:TAG] sdharbor.sdvideo.cn/av-group/voxforge[:TAG]
例如：docker tag voxforge:0.2 sdharbor.sdvideo.cn/av-group/voxforge:0.2

推送镜像到当前项目：
docker push sdharbor.sdvideo.cn/av-group/voxforge[:TAG]
例如：docker push sdharbor.sdvideo.cn/av-group/voxforge:0.2

