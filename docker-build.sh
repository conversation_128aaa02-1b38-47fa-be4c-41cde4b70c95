#!/bin/bash

# 颜色定义
GREEN="\033[0;32m"
YELLOW="\033[1;33m"
BLUE="\033[0;34m"
RED="\033[0;31m"
NC="\033[0m" # No Color

echo -e "${BLUE}=== VoxForge TTS Docker 构建脚本 ===${NC}"

# 设置代理参数
PROXY_ARGS=""
if [ -n "$http_proxy" ]; then
    PROXY_ARGS="$PROXY_ARGS --build-arg HTTP_PROXY=$http_proxy"
    echo -e "${GREEN}检测到 HTTP 代理: $http_proxy${NC}"
fi
if [ -n "$https_proxy" ]; then
    PROXY_ARGS="$PROXY_ARGS --build-arg HTTPS_PROXY=$https_proxy"
    echo -e "${GREEN}检测到 HTTPS 代理: $https_proxy${NC}"
fi
if [ -n "$all_proxy" ]; then
    PROXY_ARGS="$PROXY_ARGS --build-arg ALL_PROXY=$all_proxy"
    echo -e "${GREEN}检测到 ALL 代理: $all_proxy${NC}"
fi

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker 未安装。请先安装 Docker。${NC}"
    exit 1
fi

# 检查 docker compose 是否安装
if ! docker compose version &> /dev/null; then
    echo -e "${YELLOW}警告: Docker Compose 未安装。将只使用 Docker 命令构建。${NC}"
    USE_COMPOSE=false
else
    echo -e "${GREEN}Docker Compose 已安装。${NC}"
    USE_COMPOSE=true
fi

# 创建输出目录
mkdir -p output
echo -e "${GREEN}已创建输出目录: output/${NC}"

# 架构选择
echo -e "${YELLOW}请选择目标架构:${NC}"
echo "1) amd64 (例如 x86_64 Ubuntu 服务器)"
echo "2) arm64 (例如 Apple M 系列 Mac)"
read -p "请输入选项 [1-2]: " arch_option

case $arch_option in
    1)
        TARGET_PLATFORM="linux/amd64"
        ;;
    2)
        TARGET_PLATFORM="linux/arm64"
        ;;
    *)
        echo -e "${RED}无效选项!${NC}"
        exit 1
        ;;
esac

echo -e "${GREEN}选择的目标平台: ${TARGET_PLATFORM}${NC}"

# 构建选项
echo -e "${YELLOW}请选择操作:${NC}"
echo "1) 仅构建 Docker 镜像 并推送"
echo "2) 使用 Docker Compose 构建并运行"
echo "3) 停止并删除容器"
read -p "请输入选项 [1-3]: " option

IMAGE_TAG="sdharbor.sdvideo.cn/av-group/voxforge:0.2"

case $option in
    1)
        echo -e "${BLUE}正在构建 Docker 镜像 (${TARGET_PLATFORM})...${NC}"
        docker build --platform $TARGET_PLATFORM $PROXY_ARGS -t $IMAGE_TAG .
        echo -e "${GREEN}镜像构建完成!${NC}"

        echo -e "${BLUE}正在推送镜像到私服...${NC}"
        docker push $IMAGE_TAG
        echo -e "${GREEN}镜像已推送到私服!${NC}"
        ;;
    2)
        echo -e "${BLUE}正在构建 Docker 镜像 (${TARGET_PLATFORM})...${NC}"
        docker build --platform $TARGET_PLATFORM $PROXY_ARGS -t $IMAGE_TAG .
        if [ "$USE_COMPOSE" = true ]; then
            echo -e "${BLUE}使用 Docker Compose 构建并运行...${NC}"
            docker compose up -d
            echo -e "${GREEN}服务已启动!${NC}"
            echo -e "API 服务地址: ${BLUE}http://localhost:8000${NC}"
            echo -e "查看日志: ${YELLOW}docker compose logs -f${NC}"
        else
            echo -e "${RED}错误: Docker Compose 未安装，无法执行此选项。${NC}"
            exit 1
        fi
        ;;
    3)
        echo -e "${BLUE}停止并删除容器...${NC}"
        if [ "$USE_COMPOSE" = true ]; then
            docker compose down
        fi
        echo -e "${GREEN}容器已停止并删除!${NC}"
        ;;
    *)
        echo -e "${RED}无效选项!${NC}"
        exit 1
        ;;
esac

# 清理悬空镜像
echo -e "${BLUE}正在清理悬空镜像...${NC}"
docker image prune -f
echo -e "${GREEN}悬空镜像已清理!${NC}"

echo -e "${GREEN}操作完成!${NC}"
