#!/bin/bash

# VoxForge TTS 多架构镜像推送脚本

# 颜色定义
GREEN="\033[0;32m"
YELLOW="\033[1;33m"
BLUE="\033[0;34m"
RED="\033[0;31m"
NC="\033[0m" # No Color

echo -e "${BLUE}=== VoxForge TTS 多架构镜像推送脚本 ===${NC}"

BASE_IMAGE="sdharbor.sdvideo.cn/av-group/voxforge:0.2"
AMD64_IMAGE="${BASE_IMAGE}-amd64"
ARM64_IMAGE="${BASE_IMAGE}-arm64"

# 检查镜像是否存在
echo -e "${BLUE}检查本地镜像...${NC}"

if docker image inspect $AMD64_IMAGE >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 找到 AMD64 镜像: ${AMD64_IMAGE}${NC}"
    HAS_AMD64=true
else
    echo -e "${YELLOW}✗ 未找到 AMD64 镜像: ${AMD64_IMAGE}${NC}"
    HAS_AMD64=false
fi

if docker image inspect $ARM64_IMAGE >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 找到 ARM64 镜像: ${ARM64_IMAGE}${NC}"
    HAS_ARM64=true
else
    echo -e "${YELLOW}✗ 未找到 ARM64 镜像: ${ARM64_IMAGE}${NC}"
    HAS_ARM64=false
fi

if [ "$HAS_AMD64" = false ] && [ "$HAS_ARM64" = false ]; then
    echo -e "${RED}错误: 没有找到任何架构的镜像。请先构建镜像。${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}请选择操作:${NC}"
echo "1) 推送所有可用的架构镜像"
echo "2) 仅推送 AMD64 镜像"
echo "3) 仅推送 ARM64 镜像"
echo "4) 创建并推送多架构 manifest"
read -p "请输入选项 [1-4]: " option

case $option in
    1)
        echo -e "${BLUE}推送所有可用的架构镜像...${NC}"
        if [ "$HAS_AMD64" = true ]; then
            echo -e "${BLUE}推送 AMD64 镜像...${NC}"
            docker push $AMD64_IMAGE
        fi
        if [ "$HAS_ARM64" = true ]; then
            echo -e "${BLUE}推送 ARM64 镜像...${NC}"
            docker push $ARM64_IMAGE
        fi
        ;;
    2)
        if [ "$HAS_AMD64" = true ]; then
            echo -e "${BLUE}推送 AMD64 镜像...${NC}"
            docker push $AMD64_IMAGE
        else
            echo -e "${RED}错误: AMD64 镜像不存在${NC}"
            exit 1
        fi
        ;;
    3)
        if [ "$HAS_ARM64" = true ]; then
            echo -e "${BLUE}推送 ARM64 镜像...${NC}"
            docker push $ARM64_IMAGE
        else
            echo -e "${RED}错误: ARM64 镜像不存在${NC}"
            exit 1
        fi
        ;;
    4)
        echo -e "${BLUE}创建并推送多架构 manifest...${NC}"
        
        # 先推送各架构镜像
        if [ "$HAS_AMD64" = true ]; then
            echo -e "${BLUE}推送 AMD64 镜像...${NC}"
            docker push $AMD64_IMAGE
        fi
        if [ "$HAS_ARM64" = true ]; then
            echo -e "${BLUE}推送 ARM64 镜像...${NC}"
            docker push $ARM64_IMAGE
        fi
        
        # 创建 manifest
        echo -e "${BLUE}创建多架构 manifest...${NC}"
        MANIFEST_IMAGES=""
        if [ "$HAS_AMD64" = true ]; then
            MANIFEST_IMAGES="$MANIFEST_IMAGES $AMD64_IMAGE"
        fi
        if [ "$HAS_ARM64" = true ]; then
            MANIFEST_IMAGES="$MANIFEST_IMAGES $ARM64_IMAGE"
        fi
        
        docker manifest create $BASE_IMAGE $MANIFEST_IMAGES
        docker manifest push $BASE_IMAGE
        
        echo -e "${GREEN}多架构 manifest 已创建并推送: ${BASE_IMAGE}${NC}"
        ;;
    *)
        echo -e "${RED}无效选项!${NC}"
        exit 1
        ;;
esac

echo -e "${GREEN}操作完成!${NC}"
echo ""
echo -e "${BLUE}可用的镜像标签:${NC}"
if [ "$HAS_AMD64" = true ]; then
    echo -e "  ${YELLOW}${AMD64_IMAGE}${NC} (AMD64)"
fi
if [ "$HAS_ARM64" = true ]; then
    echo -e "  ${YELLOW}${ARM64_IMAGE}${NC} (ARM64)"
fi
if [ "$option" = "4" ]; then
    echo -e "  ${YELLOW}${BASE_IMAGE}${NC} (多架构)"
fi
