import os
import site
from pathlib import Path
from huggingface_hub import snapshot_download

def patch_kokoro_files():
    """修补 kokoro 库文件以支持新的模型名称"""
    # Find the site-packages directory
    site_packages = site.getsitepackages()
    if not site_packages:
        print("Error: Could not find site-packages directory.")
        return False

    # There can be multiple site-package paths, we'll check them all
    kokoro_model_py = None
    kokoro_pipeline_py = None
    for sp_path in site_packages:
        model_candidate = Path(sp_path) / 'kokoro' / 'model.py'
        pipeline_candidate = Path(sp_path) / 'kokoro' / 'pipeline.py'
        if model_candidate.exists():
            kokoro_model_py = model_candidate
        if pipeline_candidate.exists():
            kokoro_pipeline_py = pipeline_candidate
        if kokoro_model_py and kokoro_pipeline_py:
            break

    success = True
    
    # 修补 model.py
    if kokoro_model_py:
        print(f"Patching {kokoro_model_py}...")
        with open(kokoro_model_py, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # Define replacements based on actual file structure
        replacements = [
            ("repo_id = 'hexgrad/Kokoro-82M'", "repo_id = 'hexgrad/Kokoro-82M-v1.1-zh'"),
            ('repo_id = "hexgrad/Kokoro-82M"', 'repo_id = "hexgrad/Kokoro-82M-v1.1-zh"'),
            ("'hexgrad/Kokoro-82M': 'kokoro-v1_0.pth'", "'hexgrad/Kokoro-82M-v1.1-zh': 'kokoro-v1_1-zh.pth'"),
            ('"hexgrad/Kokoro-82M": "kokoro-v1_0.pth"', '"hexgrad/Kokoro-82M-v1.1-zh": "kokoro-v1_1-zh.pth"')
        ]
        
        # Perform replacements
        new_content = content
        for old_str, new_str in replacements:
            if old_str in new_content:
                new_content = new_content.replace(old_str, new_str)
                changes_made += 1
                print(f"model.py: Replaced {old_str} -> {new_str}")
        
        if changes_made > 0:
            with open(kokoro_model_py, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"model.py: Made {changes_made} changes")
        else:
            print("model.py: No changes needed")
    else:
        print("Error: Could not find kokoro/model.py in any site-packages directory.")
        success = False
    
    # 修补 pipeline.py
    if kokoro_pipeline_py:
        print(f"\nPatching {kokoro_pipeline_py}...")
        with open(kokoro_pipeline_py, 'r', encoding='utf-8') as f:
            content = f.read()
        
        changes_made = 0
        
        # 替换默认的 repo_id
        if "repo_id = 'hexgrad/Kokoro-82M'" in content:
            content = content.replace(
                "repo_id = 'hexgrad/Kokoro-82M'",
                "repo_id = 'hexgrad/Kokoro-82M-v1.1-zh'"
            )
            print("pipeline.py: Replaced default repo_id")
            changes_made += 1
        
        # 替换版本检查逻辑
        if "version=None if repo_id.endswith('/Kokoro-82M') else '1.1'" in content:
            content = content.replace(
                "version=None if repo_id.endswith('/Kokoro-82M') else '1.1'",
                "version='1.1' if repo_id.endswith('/Kokoro-82M-v1.1-zh') else None"
            )
            print("pipeline.py: Updated version check logic")
            changes_made += 1
        
        if changes_made > 0:
            with open(kokoro_pipeline_py, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"pipeline.py: Made {changes_made} changes")
        else:
            print("pipeline.py: No changes needed")
    else:
        print("Error: Could not find kokoro/pipeline.py in any site-packages directory.")
        success = False
    
    return success

def download_model():
    """Downloads the model to the local cache."""
    repo_id = "hexgrad/Kokoro-82M-v1.1-zh"
    local_dir = Path("./3rd")
    model_dir = local_dir / f"models--hexgrad--Kokoro-82M-v1.1-zh"
    
    # 创建目录
    local_dir.mkdir(exist_ok=True)
    
    # 检查模型是否已存在
    if model_dir.exists() and (model_dir / "config.json").exists():
        print(f"模型已存在于 {model_dir}，跳过下载")
        return True
    
    print(f"下载模型 {repo_id} 到 {model_dir}...")
    
    # 设置 Hugging Face 缓存目录
    os.environ['HF_HOME'] = str(local_dir)
    
    try:
        # 使用 snapshot_download 下载模型
        snapshot_download(
            repo_id=repo_id, 
            local_dir=model_dir,
            local_dir_use_symlinks=False, 
            resume_download=True
        )
        print("模型下载完成")
        return True
    except Exception as e:
        print(f"模型下载失败: {e}")
        return False

def main():
    """主函数：修补文件并下载模型"""
    # 执行修补操作
    patch_success = patch_kokoro_files()
    if not patch_success:
        print("\n修补库文件失败，但仍将尝试下载模型")
    
    # 下载模型
    print("\n开始下载模型...")
    download_success = download_model()
    
    # 检查结果
    if patch_success and download_success:
        print("\n所有操作完成：库文件修补成功，模型下载成功")
        return True
    elif download_success:
        print("\n模型下载成功，但库文件修补失败")
        return True
    else:
        print("\n模型下载失败")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("准备工作失败，退出程序")
        exit(1)
    else:
        print("准备工作完成，可以启动服务")
        exit(0)