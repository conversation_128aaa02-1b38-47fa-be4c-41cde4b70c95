#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile
#
--index-url https://pypi.tuna.tsinghua.edu.cn/simple
addict==2.4.0
    # via misaki
annotated-types==0.7.0
    # via pydantic
attrs==25.3.0
    # via
    #   csvw
    #   jsonschema
    #   phonemizer-fork
    #   referencing
babel==2.17.0
    # via csvw
blinker==1.9.0
    # via flask
blis==1.3.0
    # via thinc
catalogue==2.0.10
    # via
    #   spacy
    #   srsly
    #   thinc
certifi==2025.7.9
    # via requests
cffi==1.17.1
    # via soundfile
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   flask
    #   typer
cloudpathlib==0.21.1
    # via weasel
cn2an==0.5.23
    # via -r requirements.in
colorama==0.4.6
    # via csvw
confection==0.1.5
    # via
    #   thinc
    #   weasel
csvw==3.5.1
    # via segments
curated-tokenizers==0.0.9
    # via spacy-curated-transformers
curated-transformers==0.1.1
    # via spacy-curated-transformers
cymem==2.0.11
    # via
    #   preshed
    #   spacy
    #   thinc
dlinfo==2.0.0
    # via phonemizer-fork
docopt==0.6.2
    # via num2words
espeakng-loader==0.2.4
    # via misaki
filelock==3.18.0
    # via
    #   huggingface-hub
    #   torch
    #   transformers
flask==3.1.1
    # via -r requirements.in
fsspec==2025.5.1
    # via
    #   huggingface-hub
    #   torch
gevent==25.5.1
    # via -r requirements.in
greenlet==3.2.3
    # via gevent
hf-xet==1.1.5
    # via huggingface-hub
huggingface-hub==0.33.2
    # via
    #   -r requirements.in
    #   kokoro
    #   tokenizers
    #   transformers
idna==3.10
    # via requests
isodate==0.7.2
    # via csvw
itsdangerous==2.2.0
    # via flask
jieba==0.42.1
    # via -r requirements.in
jinja2==3.1.6
    # via
    #   flask
    #   spacy
    #   torch
joblib==1.5.1
    # via phonemizer-fork
jsonschema==4.24.0
    # via csvw
jsonschema-specifications==2025.4.1
    # via jsonschema
kokoro==0.9.4
    # via -r requirements.in
langcodes==3.5.0
    # via spacy
language-data==1.3.0
    # via langcodes
language-tags==1.2.0
    # via csvw
loguru==0.7.3
    # via kokoro
marisa-trie==1.2.1
    # via language-data
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via
    #   flask
    #   jinja2
    #   werkzeug
mdurl==0.1.2
    # via markdown-it-py
misaki[en]==0.9.4
    # via kokoro
mpmath==1.3.0
    # via sympy
murmurhash==1.0.13
    # via
    #   preshed
    #   spacy
    #   thinc
networkx==3.5
    # via torch
num2words==0.5.14
    # via misaki
numpy==2.3.1
    # via
    #   -r requirements.in
    #   blis
    #   kokoro
    #   soundfile
    #   spacy
    #   thinc
    #   transformers
ordered-set==4.1.0
    # via -r requirements.in
packaging==25.0
    # via
    #   huggingface-hub
    #   spacy
    #   thinc
    #   transformers
    #   weasel
phonemizer-fork==3.3.2
    # via misaki
preshed==3.0.10
    # via
    #   spacy
    #   thinc
proces==0.1.7
    # via cn2an
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via
    #   confection
    #   spacy
    #   thinc
    #   weasel
pydantic-core==2.33.2
    # via pydantic
pygments==2.19.2
    # via rich
pyparsing==3.2.3
    # via rdflib
pypinyin==0.54.0
    # via
    #   -r requirements.in
    #   pypinyin-dict
pypinyin-dict==0.9.0
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via csvw
pyyaml==6.0.2
    # via
    #   huggingface-hub
    #   transformers
rdflib==7.1.4
    # via csvw
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via
    #   curated-tokenizers
    #   misaki
    #   segments
    #   transformers
requests==2.32.4
    # via
    #   csvw
    #   huggingface-hub
    #   spacy
    #   transformers
    #   weasel
rfc3986==1.5.0
    # via csvw
rich==14.0.0
    # via typer
rpds-py==0.26.0
    # via
    #   jsonschema
    #   referencing
safetensors==0.5.3
    # via transformers
segments==2.3.0
    # via phonemizer-fork
shellingham==1.5.4
    # via typer
six==1.17.0
    # via python-dateutil
smart-open==7.3.0.post1
    # via weasel
soundfile==0.13.1
    # via -r requirements.in
spacy==3.8.7
    # via misaki
spacy-curated-transformers==0.3.1
    # via misaki
spacy-legacy==3.0.12
    # via spacy
spacy-loggers==1.0.5
    # via spacy
srsly==2.5.1
    # via
    #   confection
    #   spacy
    #   thinc
    #   weasel
sympy==1.14.0
    # via torch
thinc==8.3.6
    # via spacy
tokenizers==0.21.2
    # via transformers
torch==2.7.1
    # via
    #   -r requirements.in
    #   curated-transformers
    #   kokoro
    #   spacy-curated-transformers
tqdm==4.67.1
    # via
    #   -r requirements.in
    #   huggingface-hub
    #   spacy
    #   transformers
transformers==4.53.1
    # via kokoro
typer==0.16.0
    # via
    #   spacy
    #   weasel
typing-extensions==4.14.1
    # via
    #   huggingface-hub
    #   phonemizer-fork
    #   pydantic
    #   pydantic-core
    #   referencing
    #   torch
    #   typer
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
uritemplate==4.2.0
    # via csvw
urllib3==2.5.0
    # via requests
wasabi==1.1.3
    # via
    #   spacy
    #   thinc
    #   weasel
weasel==0.4.1
    # via spacy
werkzeug==3.1.3
    # via flask
wrapt==1.17.2
    # via smart-open
zope-event==5.1
    # via gevent
zope-interface==7.2
    # via gevent
scipy==1.16.0

# The following packages are considered to be unsafe in a requirements file:
# setuptools
