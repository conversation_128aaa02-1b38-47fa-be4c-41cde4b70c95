services:
  voxforge-tts:
    build:
      context: .
      dockerfile: Dockerfile
    image: sdharbor.sdvideo.cn/av-group/voxforge:0.2
    container_name: voxforge-tts
    restart: unless-stopped
    ports:
      - "10909:8000"
    environment:
      - TZ=Asia/Shanghai
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=INFO
      - KOKORO_MODEL_PATH=/app/3rd/Kokoro/
      - MAX_RECURSION_DEPTH=3
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:10909/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  output:
    driver: local