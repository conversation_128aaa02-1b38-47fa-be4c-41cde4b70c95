from ast import mod
import kokoro
import soundfile as sf
import io
from flask import Flask, request, jsonify, send_file
import uuid
import logging
import tqdm
import re
import numpy as np
from scipy.signal import resample

# 配置日志
import os
import sys

# 从环境变量获取日志级别，默认为INFO
log_level_name = os.environ.get('LOG_LEVEL', 'DEBUG')
log_level = getattr(logging, log_level_name.upper(), logging.INFO)

logging.basicConfig(
    level=log_level,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logging.info(f"日志级别设置为: {logging.getLevelName(log_level)}")
# 从环境变量获取调试模式设置
debug_mode = os.environ.get('DEBUG_MODE', 'false').lower() == 'true'
if debug_mode:
    logging.info("调试模式已启用，将提供更详细的错误信息")

max_text_length = 500

# 文本预处理函数，用于减少递归深度问题
def preprocess_text(input_text):
    """预处理文本，优化中英文混合处理，减少递归问题"""
    if not input_text:
        return input_text
    
    # 替换连续的标点符号为单个
    processed = re.sub(r'([，。！？,.!?])+', r'\1', input_text)
    
    # 在中英文之间添加空格，便于分割处理
    processed = re.sub(r'([\u4e00-\u9fff])([a-zA-Z])', r'\1 \2', processed)
    processed = re.sub(r'([a-zA-Z])([\u4e00-\u9fff])', r'\1 \2', processed)
    
    # 保留中文、英文、数字、常用标点和空白字符
    processed = re.sub(r'[^\w\s，。！？,.!?:;"\'\(\)\[\]【】《》、～~@#￥%…&*（）\-—_=+\n\u4e00-\u9fff]', ' ', processed)
    
    # 合并多个空格为一个
    processed = re.sub(r'\s+', ' ', processed)
    
    return processed.strip()

def split_text_by_language_and_punctuation(text: str) -> list[str]:
    """
    将文本按照语言类型（中文段、英文段）和标点符号进行分段
    优化处理中英文混合文本，确保句子完整性
    """
    # 预处理文本
    text = preprocess_text(text)
    if not text:
        return []
    
    # 定义分段结果
    segments = []
    
    # 1. 首先按照主要终结符分段（句号、问号、感叹号）
    # 中文终结符
    chinese_pattern = re.compile(r'([^。！？]*[。！？])')
    # 英文终结符
    english_pattern = re.compile(r'([^.!?]*[.!?])')
    
    # 检查文本是否包含中文
    has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
    
    if has_chinese:
        # 处理中文文本
        primary_segments = chinese_pattern.findall(text)
        # 处理剩余文本
        rest = chinese_pattern.sub('', text).strip()
    else:
        # 处理英文文本
        primary_segments = english_pattern.findall(text)
        # 处理剩余文本
        rest = english_pattern.sub('', text).strip()
    
    # 添加主要分段
    segments.extend([s.strip() for s in primary_segments if s.strip()])
    
    # 2. 处理剩余文本（没有主要终结符的部分）
    if rest:
        # 如果剩余文本包含次要分隔符（逗号、分号等），按次要分隔符分段
        if has_chinese and '，' in rest:
            # 中文逗号分段
            comma_segments = rest.split('，')
            for i in range(len(comma_segments) - 1):
                if comma_segments[i].strip():
                    segments.append(comma_segments[i].strip() + '，')
            if comma_segments[-1].strip():
                segments.append(comma_segments[-1].strip())
        elif not has_chinese and ',' in rest:
            # 英文逗号分段
            comma_segments = rest.split(',')
            for i in range(len(comma_segments) - 1):
                if comma_segments[i].strip():
                    segments.append(comma_segments[i].strip() + ',')
            if comma_segments[-1].strip():
                segments.append(comma_segments[-1].strip())
        else:
            # 没有次要分隔符，将剩余文本作为一个段落
            segments.append(rest)
    
    # 3. 合并过短的段落（少于5个字符的段落与前一段合并）
    if len(segments) > 1:
        merged_segments = [segments[0]]
        for i in range(1, len(segments)):
            current = segments[i]
            # 如果当前段落很短，与前一段合并
            if len(current) < 5 and merged_segments:
                merged_segments[-1] += current
            else:
                merged_segments.append(current)
        segments = merged_segments
    
    # 如果没有有效段落，返回原文本
    if not segments and text.strip():
        return [text.strip()]
    
    return segments


# 从环境变量获取文本分隔模式
default_split_pattern = r'[，。！？,.!?\n]+'

# 增加Python递归限制，防止处理复杂文本时出现递归深度超限错误
default_recursion_limit = sys.getrecursionlimit()
new_recursion_limit = 5000  # 设置更高的递归限制
sys.setrecursionlimit(new_recursion_limit)
logging.info(f"递归深度限制从 {default_recursion_limit} 增加到 {new_recursion_limit}")

# 设置模型路径
KokoroModelPath = os.environ.get('KOKORO_MODEL_PATH', "/Users/<USER>/CLionProjects/VoxForge/3rd/Kokoro/")

logging.info(f"KokoroModelPath: {KokoroModelPath}")
# 导入并初始化 kokoro (模型已在构建时准备完成)
logging.info("初始化 Kokoro TTS 模型...")
from kokoro import KPipeline
from kokoro import KModel

logging.info(f"kokoro.__version__ {kokoro.__version__}")
logging.info(f"Kokoro module file: {kokoro.__file__}")
import datetime

logging.info(f"Kokoro time:{datetime.datetime.fromtimestamp(os.path.getmtime(kokoro.__file__))}")

logging.info("KModel 开始")
SAMPLE_RATE = 24000
TARGET_SAMPLE_RATE = 8000  # PCMA 的标准采样率
# How much silence to insert between paragraphs: 5000 is about 0.2 seconds
N_ZEROS = 5000
# 可用的音色列表
AVAILABLE_VOICES = {
}
# 从 KokoroModelPath+ "voices/" 路径下读取可用的音色列表 音色列表要裁剪到后面的.pt
voices_dir = KokoroModelPath + "voices/"
available_voices = [f[:-3] for f in os.listdir(voices_dir) if
                    os.path.isfile(os.path.join(voices_dir, f)) and f.endswith('.pt')]
logging.info(f"从 {voices_dir} 路径下发现可用音色: {available_voices}")

# 合并环境变量配置的音色列表和 voices 目录下的音色列表
AVAILABLE_VOICES.update({voice: voice for voice in available_voices})
logging.info(f"合并音色列表: {AVAILABLE_VOICES}")

# 兼容原有代码的模型引用
model = KModel(config=KokoroModelPath + 'config.json', model=KokoroModelPath + 'kokoro-v1_0.pth')

# 创建英文和中文的pipeline
en_pipeline = KPipeline(lang_code='a', model=model)

def en_callable(text):
    logging.info(f"en_callable 输入文本: {text}")
    if text == 'Kokoro':
        return 'kˈOkəɹO'
    elif text == 'Sol':
        return 'sˈOl'
    # 对于中文文本，直接返回原文本，不做小写处理
    if any('\u4e00' <= char <= '\u9fff' for char in text):
        return text
    return text.lower()  # 简单地将英文文本转为小写作为音素表示


# 使用中文模型初始化中文pipeline
zh_pipeline = KPipeline(lang_code='z', model=model, en_callable=en_callable)
# zh_pipeline = KPipeline(lang_code='z', model=model)
logging.info("KPipeline 执行结束")


def linear2alaw(sample):
    """将16bit PCM样本编码为8bit G.711 A-law
    按照ITU-T G.711标准实现A-law编码
    """
    # A-law压缩参数
    A = 87.7
    # 最大值和裁剪值
    MAX_VAL = 32767
    CLIP = 32767
    
    # 归一化并裁剪样本
    sample = np.clip(sample, -CLIP, CLIP)
    
    # 获取符号位（0表示正，1表示负）
    sign = (sample < 0).astype(np.uint8)
    sample = np.abs(sample)
    
    # 归一化到[0,1]范围
    x_norm = sample / MAX_VAL
    
    # 应用A-law压缩公式
    # 对于小信号使用线性量化，大信号使用对数量化
    mask = (x_norm < 1.0/A)
    f_x = np.zeros_like(x_norm)
    f_x[mask] = A * x_norm[mask] / (1.0 + np.log(A))
    f_x[~mask] = (1.0 + np.log(A * x_norm[~mask])) / (1.0 + np.log(A))
    
    # 量化为8位（7位数据+1位符号）
    # 将[0,1]范围映射到[0,127]
    quant = np.floor(f_x * 127 + 0.5).astype(np.uint8)
    
    # 编码为A-law格式（按照G.711标准）
    # 1. 反转奇数位（bit inversion）
    quant ^= 0x55
    
    # 2. 添加符号位到最高位
    alaw = (sign << 7) | quant
    
    return alaw.astype(np.uint8)

# 调整语音生成速度，使其更适合中文文本
# 对于中文文本，使用更慢的速度以确保生成的音频时长合理
def speed_callable(len_ps):
    # 基础速度设置为0.8，较慢的速度有助于生成更自然的语音
    speed = 0.8
    
    # 对于短文本，使用正常速度
    if len_ps <= 83:
        speed = 0.95  # 略微降低短文本的速度，使其更自然
    # 对于中等长度的文本，速度随长度增加而减小
    elif len_ps < 183:
        speed = 0.95 - (len_ps - 83) / 600  # 减缓速度下降的幅度
    # 对于长文本，使用更慢的速度
    else:
        speed = 0.85 - (len_ps - 183) / 1000  # 进一步减缓速度下降的幅度
        # 设置最低速度限制，防止语音过慢
        speed = max(speed, 0.7)
    
    # 返回最终速度，不再额外增加速度
    return speed


# 创建Flask应用
app = Flask(__name__)


@app.route('/ttsStream', methods=['POST'])
def text_to_speech_stream():
    """文本转语音API接口，支持中英文混合文本"""
    # 记录请求信息
    request_id = str(uuid.uuid4())[:8]
    logging.info(f"[{request_id}] 收到ttsStream请求 - 客户端IP: {request.remote_addr}")
    logging.debug(f"[{request_id}] 请求头: {dict(request.headers)}")
    logging.debug(f"[{request_id}] 请求体: {request.get_data()}")
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            logging.warning(f"[{request_id}] 请求体为空")
            return jsonify({'error': '请求体不能为空'}), 400
        text = data.get('text', '')
        voice = data.get('voice', 'zf_xiaoxiao')  # 使用正确的默认音色
        speed = data.get('speed', 1.0)  # 添加速度参数
        logging.info(
            f"[{request_id}] 请求参数: text='{text[:30]}{'...' if len(text) > 30 else ''}', voice={voice}")
        # 参数验证
        if not text.strip():
            logging.warning(f"[{request_id}] 文本内容为空")
            return jsonify({'error': '文本内容不能为空'}), 400
        # 检查文本长度，过长可能导致递归深度超限
        if len(text) > max_text_length:
            logging.warning(f"[{request_id}] 文本过长: {len(text)} 字符")
            return jsonify({'error': f'文本过长，请限制在{max_text_length}字符以内，或分段处理'}), 400
        if voice not in AVAILABLE_VOICES:
            logging.warning(f"[{request_id}] 不支持的音色: {voice}")
            return jsonify({
                'error': f'不支持的音色: {voice}',
                'available_voices': AVAILABLE_VOICES
            }), 400
    except Exception as e3:
        import traceback
        error_trace = traceback.format_exc()
        logging.error(f"[{request_id}] 验证参数: {str(e3)}")
        logging.debug(f"[{request_id}] 错误详情: {error_trace}")
        return jsonify({
            'success': False,
            'error': f'语音生成失败: {str(e3)}'
        }), 500
    logging.info(f"[{request_id}] 参数验证结束")
    logging.info("=== 生成中英文混合音频文件 ===")
    logging.info("=== text: " + text)
    logging.info("=== voice: " + voice)
    logging.info("=== speed: " + str(speed))
    try:
        # 分割文本为句子
        text_arr = split_text_by_language_and_punctuation(text)
        logging.info(f"[{request_id}] 文本已分割为 {len(text_arr)} 个句子")
        all_audio = []  # 存储所有音频片段
        chunk_count = 0
        # 优先使用中文模型的音色路径
        voice_path = KokoroModelPath + "voices/" + voice + ".pt"
        logging.info(f"[{request_id}] 使用音色路径: {voice_path}")
        for paragraph in tqdm.tqdm(text_arr):
            # 如果paragraph是空文本跳过
            if not paragraph.strip():
                logging.warning(f"[{request_id}] 空文本段落: {paragraph}")
                continue
            logging.info(f"[{request_id}] 处理段落: [{paragraph}]")
                # 使用通用pipeline处理
            generator = zh_pipeline(
                    paragraph,
                    model=model,
                    voice=voice_path,
                    speed=speed_callable,
                )
            try:
                result = next(generator)
                wav = result.audio
                logging.info(f"[{request_id}] 段落转换完成，音频长度: {len(wav)}")
                # 在段落之间添加适当的停顿
                if chunk_count == 1 and N_ZEROS > 0:
                    # 对于中文文本，增加段落间的停顿时间
                    pause_length = N_ZEROS
                    wav = np.concatenate([np.zeros(pause_length), wav])
                all_audio.append(wav)
                chunk_count += 1
            except Exception as e:
                logging.error(f"[{request_id}] 处理段落时出错: {str(e)}")
                logging.error(f"[{request_id}] 问题段落: {paragraph}")
                # 继续处理下一个段落，而不是整个失败
        logging.info(f"[{request_id}] 所有段落处理完成，共 {chunk_count} 个段落")
        if not all_audio:
            return jsonify({
                'success': False,
                'error': f'未能生成任何音频内容'
            }), 500
        # 合并音频
        combined_audio = np.concatenate(all_audio)
        logging.info(f"[{request_id}] 音频片段合并完成，总长度: {len(combined_audio)}")

        # 重采样到 8000Hz (PCMA标准采样率)
        if SAMPLE_RATE != TARGET_SAMPLE_RATE:
            logging.info(f"[{request_id}] 重采样音频从 {SAMPLE_RATE}Hz 到 {TARGET_SAMPLE_RATE}Hz")
            num_samples = int(len(combined_audio) * TARGET_SAMPLE_RATE / SAMPLE_RATE)
            combined_audio = resample(combined_audio, num_samples)
            logging.info(f"[{request_id}] 重采样后音频长度: {len(combined_audio)}")
        
        # 转换为 16bit PCM (确保音频幅度在正确范围内)
        # 首先归一化到[-1,1]范围
        max_val = np.max(np.abs(combined_audio))
        if max_val > 0:
            combined_audio = combined_audio / max_val
        
        # 然后转换为16位PCM格式
        pcm16 = (combined_audio * 32767).astype(np.int16)
        logging.info(f"[{request_id}] PCM16转换完成，最大值: {np.max(np.abs(pcm16))}")

        # 编码为 A-law (G.711 A-law)
        alaw_audio = linear2alaw(pcm16)
        logging.info(f"[{request_id}] A-law编码完成，数据大小: {len(alaw_audio)}字节")
        
        # 写入裸 PCM 数据
        audio_io = io.BytesIO()
        audio_io.write(alaw_audio.tobytes())
        audio_io.seek(0)
        
        # 保存一份WAV格式的音频用于调试
        try:
            debug_audio_path = "/tmp/debug_audio.wav"
            sf.write(debug_audio_path, combined_audio, TARGET_SAMPLE_RATE, format='WAV')
            logging.info(f"[{request_id}] 已保存调试用WAV文件到: {debug_audio_path}")
        except Exception as e:
            logging.warning(f"[{request_id}] 保存调试音频失败: {str(e)}")
        
        # 返回PCMA格式的音频
        return send_file(
            audio_io,
            mimetype='audio/x-alaw-basic',  # 更精确的MIME类型
            as_attachment=False,
            download_name='tts.pcma'
        )
        # wav 格式
        # audio_io = io.BytesIO()
        # sf.write(audio_io, combined_audio, SAMPLE_RATE, format='WAV')
        # audio_io.seek(0)  # 重置指针
        # # sf.write("/app/3rd/Kokoro/tts.wav", combined_audio, SAMPLE_RATE)
        # # 返回音频流（以 WAV 格式）
        # return send_file(
        #     audio_io,
        #     mimetype='audio/wav',
        #     as_attachment=False,
        #     download_name='tts.wav'
        # )
    except Exception as e2:
        import traceback
        error_trace = traceback.format_exc()
        logging.error(f"[{request_id}] 音频生成错误: {str(e2)}")
        logging.debug(f"[{request_id}] 错误详情: {error_trace}")
        return jsonify({
            'success': False,
            'error': f'语音生成失败: {str(e2)}'
        }), 500



@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    request_id = str(uuid.uuid4())[:8]
    logging.debug(f"[{request_id}] 收到健康检查请求 - 客户端IP: {request.remote_addr}")
    return jsonify({
        'status': 'healthy',
        'service': 'VoxForge TTS API',
        'model_ready': True
    })



if __name__ == '__main__':
    # 记录系统信息
    import platform
    logging.info("VoxForge TTS API 服务启动中...")
    logging.info(f"系统信息: Python {sys.version.split()[0]}, {platform.system()} {platform.version()}")

    # 尝试使用gevent启动（非阻塞）
    from gevent.pywsgi import WSGIServer
    from gevent import monkey

    host = '0.0.0.0'
    port = 8000

    # 调试模式，使用 Flask 自带服务器
    # app.run(debug=False, port=port, host=host)
    # if sys.gettrace() is not None:
    # else:
    # 生产模式，使用 gevent 非阻塞服务器
    from gevent import monkey

    monkey.patch_all()
    from gevent.pywsgi import WSGIServer

    logging.info(f"使用gevent启动服务器（非阻塞模式）在 {host}:{port}...")
    logging.info("服务器准备就绪，等待请求...")
    http_server = WSGIServer((host, port), app)
    http_server.serve_forever()
